backtest:
  cash: 30000
  commission: 0.0
  hedging: false
  margin: 0.1
  trade_on_close: true
  exclusive_orders: true
  finalize_trades: true
data:
  date_column: 'date'
  file_path: 'C:\Users\<USER>\Desktop\Python\data\NIFTY_1min.csv'
  index_column: 'date'
  parse_dates: true
  required_columns:
  - Open
  - High
  - Low
  - Close
  - Volume
  start_date: '2015-01-01'
  end_date: '2025-12-31'
  timeframe: 30
logging:
  enable_debug: false
  enable_detailed_metrics: true
output:
  required_trade_columns:
  - Index
  - Entry DateTime
  - Exit DateTime
  - Entry Price
  - Exit Price
  - Profit Points
  - Profit Percent
  - Position
  - Trade Duration
  - Exit Reason
  - PA Signal
  - Price Action Pattern
  trade_log_file: simple_price_action_trades.csv
strategy:
  # Time settings
  market_start_time: '09:15'
  market_end_time: '15:30'
  entry_start_time: '09:30'
  exit_end_time: '15:15'
  avoid_lunch_hour: false
  # Intraday-only settings
  intraday_only: true
  force_exit_time: '15:15'
  stop_new_trades_time: '15:00'
  aggressive_eod_exit: true
  time_filters:
    lunch_hour_start: '12:00'
    lunch_hour_end: '13:00'
    hour_transition_avoid_minutes: 2
    market_opening_duration_minutes: 5
  
  # Indicator periods
  atr_period: 10
  fast_ma_period: 5
  slow_ma_period: 21
  rsi_period: 10
  rsi_overbought: 98
  rsi_oversold: 25
  use_rsi_filter: true
  
  # Risk management
  sl_atr_multiplier: 0.4
  tp_rr: 2.8
  position_size: 0.99
  
  # Entry filters
  entry_filters:
    enable_advanced_filters: true
    enable_key_level_filter: false
    enable_time_filter: true
    enable_trend_filter: true
    enable_volatility_filter: true
    key_level_threshold: 1.0
    trend_filter_period: 10
    volatility_multiplier: 1.0
  
  # Price action settings
  breakout_lookback: 5
  price_action:
    signal_threshold: 0.10
    signal_threshold_multiplier: 1.0
    volatility_check_multiplier: 0.40
    weight_breakout: 3.0
    weight_engulfing: 3.0
    weight_inside_bar: 0.5
    weight_outside_bar: 2.5
    weight_pinbar: 3.0
    choppy_market:
      lookback_bars: 3
      range_percent_threshold: 0.16
  
  # Pattern recognition
  price_action_patterns:
    breakout_strength:
      lookback: 10
      threshold: 0.35
    engulfing:
      threshold: 0.0
    pin_bars:
      body_ratio: 0.35
      tail_ratio: 0.5
    take_profit:
      num_key_levels: 1
  
  # Technical analysis
  technical_analysis:
    average_atr:
      default_lookback: 60
    key_levels:
      clustering_threshold: 0.0005
      default_lookback: 120
      default_num_levels: 1
      window_size: 1
    trend_calculation:
      min_data_points: 5
      slope_periods: 3
  
  # Trade management
  trade_management:
    take_profit:
      atr_buffer: 0.10
      distance_multiplier: 1.5
      recent_price_lookback: 8
    time_based_exit:
      long_duration_minutes: 240
      long_duration_profit_percent: 1.50
      medium_duration_minutes: 150
      medium_duration_profit_percent: 1.00
      loss_cut_minutes: 30
      loss_cut_percent: -0.60
    trailing_stop:
      profit_thresholds:
      - 1.2
      - 0.8
      - 0.5
      lock_in_percentages:
      - 0.5
      - 0.3
      - 0.15
  # Output formatting
  output_formatting:
    datetime_format: '%Y-%m-%d %H:%M:%S'
    decimal_precision: 2
